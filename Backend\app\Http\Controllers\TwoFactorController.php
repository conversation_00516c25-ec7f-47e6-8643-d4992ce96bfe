<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use App\Traits\ApiResponseTrait;

class TwoFactorController extends Controller
{
    use ApiResponseTrait;

    /**
     * Get current 2FA status for the authenticated user
     */
    public function status()
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return $this->errorResponse('User not authenticated', 401);
            }

            $data = [
                'two_factor_enabled' => (bool) $user->two_factor_enabled,
                'two_factor_enabled_at' => $user->two_factor_enabled_at,
                'two_factor_always_required' => (bool) $user->two_factor_always_required,
            ];

            return $this->successResponse(
                $data,
                '2FA status retrieved successfully'
            );
        } catch (\Exception $e) {
            Log::error('Error fetching 2FA status: ' . $e->getMessage());
            return $this->errorResponse('Failed to fetch 2FA status', 500);
        }
    }

    /**
     * Enable two-factor authentication for the user
     */
    public function enable(Request $request)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return $this->errorResponse('User not authenticated', 401);
            }

            // Check if 2FA is already enabled
            if ($user->two_factor_enabled) {
                return $this->errorResponse('Two-factor authentication is already enabled', 400);
            }

            // Generate a simple secret for now (in production, you'd use a proper TOTP library)
            $secret = $this->generateSecret();

            // Update user record
            $user->update([
                'two_factor_enabled' => true,
                'two_factor_secret' => encrypt($secret), // Encrypt the secret
                'two_factor_enabled_at' => now(),
            ]);

            Log::info('2FA enabled for user: ' . $user->id);

            return $this->successResponse(
                [
                    'two_factor_enabled' => true,
                    'two_factor_enabled_at' => $user->two_factor_enabled_at,
                ],
                'Two-factor authentication has been enabled successfully'
            );
        } catch (\Exception $e) {
            Log::error('Error enabling 2FA: ' . $e->getMessage());
            return $this->errorResponse('Failed to enable two-factor authentication', 500);
        }
    }

    /**
     * Disable two-factor authentication for the user
     */
    public function disable(Request $request)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return $this->errorResponse('User not authenticated', 401);
            }

            // Check if 2FA is already disabled
            if (!$user->two_factor_enabled) {
                return $this->errorResponse('Two-factor authentication is already disabled', 400);
            }

            // Update user record
            $user->update([
                'two_factor_enabled' => false,
                'two_factor_secret' => null,
                'two_factor_enabled_at' => null,
                'two_factor_always_required' => false, // Reset always required when disabling 2FA
            ]);

            Log::info('2FA disabled for user: ' . $user->id);

            return $this->successResponse(
                [
                    'two_factor_enabled' => false,
                    'two_factor_enabled_at' => null,
                    'two_factor_always_required' => false,
                ],
                'Two-factor authentication has been disabled successfully'
            );
        } catch (\Exception $e) {
            Log::error('Error disabling 2FA: ' . $e->getMessage());
            return $this->errorResponse('Failed to disable two-factor authentication', 500);
        }
    }

    /**
     * Generate a restore code for 2FA recovery
     */
    public function generateRestoreCode()
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return $this->errorResponse('User not authenticated', 401);
            }

            // Check if 2FA is enabled
            if (!$user->two_factor_enabled) {
                return $this->errorResponse('Two-factor authentication must be enabled to generate restore code', 400);
            }

            // Generate restore code
            $restoreCode = $this->generateRestoreCodeString();

            // Update user record with new restore code
            $user->update([
                'two_factor_secret' => encrypt($restoreCode),
            ]);

            $data = [
                'restore_code' => $restoreCode,
                'generated_at' => now(),
            ];

            return $this->successResponse(
                $data,
                'Restore code generated successfully'
            );
        } catch (\Exception $e) {
            Log::error('Error generating restore code: ' . $e->getMessage());
            return $this->errorResponse('Failed to generate restore code', 500);
        }
    }

    /**
     * Verify restore code for security verification
     */
    public function verifyRestoreCode(Request $request)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return $this->errorResponse('User not authenticated', 401);
            }

            // Check if 2FA is enabled
            if (!$user->two_factor_enabled) {
                return $this->errorResponse('Two-factor authentication must be enabled to use restore code', 400);
            }

            $request->validate([
                'restore_code' => 'required|string',
                'session_id' => 'required|string|uuid'
            ]);

            // Get the stored restore code
            if (!$user->two_factor_secret) {
                return $this->errorResponse('No restore code found for this account', 400);
            }

            try {
                $storedRestoreCode = decrypt($user->two_factor_secret);
            } catch (\Exception $e) {
                Log::error('Failed to decrypt restore code for user: ' . $user->id);
                return $this->errorResponse('Invalid restore code configuration', 500);
            }

            // Verify the restore code
            if ($storedRestoreCode !== $request->restore_code) {
                Log::warning('Invalid restore code attempt for user: ' . $user->id);
                return $this->errorResponse('Invalid restore code', 400);
            }

            // Generate new restore code immediately after successful verification
            $newRestoreCode = $this->generateRestoreCodeString();

            // Update user record with new restore code
            $user->update([
                'two_factor_secret' => encrypt($newRestoreCode),
            ]);

            Log::info('Restore code verified and regenerated for user: ' . $user->id);

            return $this->successResponse(
                [
                    'verified' => true,
                    'new_restore_code' => $newRestoreCode,
                    'session_id' => $request->session_id
                ],
                'Restore code verified successfully'
            );

        } catch (\Exception $e) {
            Log::error('Error verifying restore code: ' . $e->getMessage());
            return $this->errorResponse('Failed to verify restore code', 500);
        }
    }

    /**
     * Update the always require 2FA setting
     */
    public function updateAlwaysRequired(Request $request)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return $this->errorResponse('User not authenticated', 401);
            }

            // Check if 2FA is enabled
            if (!$user->two_factor_enabled) {
                return $this->errorResponse('Two-factor authentication must be enabled to change this setting', 400);
            }

            $request->validate([
                'always_required' => 'required|boolean'
            ]);

            // Update user record
            $user->update([
                'two_factor_always_required' => $request->always_required,
            ]);

            Log::info('2FA always required setting updated for user: ' . $user->id, [
                'always_required' => $request->always_required
            ]);

            return $this->successResponse(
                [
                    'two_factor_always_required' => (bool) $user->two_factor_always_required,
                ],
                'Always require 2FA setting updated successfully'
            );
        } catch (\Exception $e) {
            Log::error('Error updating 2FA always required setting: ' . $e->getMessage());
            return $this->errorResponse('Failed to update always require 2FA setting', 500);
        }
    }

    /**
     * Generate a simple secret for 2FA
     * In production, you would use a proper TOTP library like Google Authenticator
     */
    private function generateSecret()
    {
        // Generate a 32-character base32 secret
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
        $secret = '';
        for ($i = 0; $i < 32; $i++) {
            $secret .= $chars[random_int(0, strlen($chars) - 1)];
        }
        return $secret;
    }

    /**
     * Generate a restore code string
     */
    private function generateRestoreCodeString()
    {
        $chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
        $code = '';
        for ($i = 0; $i < 24; $i++) {
            $code .= $chars[random_int(0, strlen($chars) - 1)];
        }
        // Format as XXXX-XXXX-XXXX-XXXX-XXXX-XXXX
        return implode('-', str_split($code, 4));
    }
}
