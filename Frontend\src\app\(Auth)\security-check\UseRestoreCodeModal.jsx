'use client';
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';
import React, { useState, useRef } from 'react';
import { Formik, Field, Form } from 'formik';
import * as Yup from 'yup';
import InputError from '@/Components/UI/InputError';
import '@/css/account/Security.scss';

const restoreCodeSchema = Yup.object().shape({
  restore_code: Yup.string()
    .required('Restore code is required')
    .matches(/^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/, 'Invalid restore code format')
});

const UseRestoreCodeModal = ({ show, handleClose, onSubmit, isSubmitting }) => {
  const inputRefs = useRef([]);
  const [restoreCode, setRestoreCode] = useState('');

  const formatRestoreCode = (value) => {
    // Remove all non-alphanumeric characters and convert to uppercase
    const cleaned = value.replace(/[^A-Z0-9]/gi, '').toUpperCase();
    
    // Split into groups of 4 characters
    const groups = [];
    for (let i = 0; i < cleaned.length; i += 4) {
      groups.push(cleaned.slice(i, i + 4));
    }
    
    // Join with dashes, limit to 6 groups (24 characters total)
    return groups.slice(0, 6).join('-');
  };

  const handleInputChange = (e, setFieldValue) => {
    const value = e.target.value;
    const formatted = formatRestoreCode(value);
    setRestoreCode(formatted);
    setFieldValue('restore_code', formatted);
  };

  const handlePaste = (e, setFieldValue) => {
    e.preventDefault();
    const pasteData = e.clipboardData.getData('text');
    const formatted = formatRestoreCode(pasteData);
    setRestoreCode(formatted);
    setFieldValue('restore_code', formatted);
  };

  const handleSubmitForm = (values) => {
    onSubmit(values.restore_code);
  };

  return (
    <Modal
      show={show}
      onHide={handleClose}
      centered
      size="lg"
      contentClassName="custom-modal-content"
    >
      <div className="px-4 sm:px-6 py-6 sm:py-8 rounded-[15px] space-y-4 sm:space-y-6 max-w-full overflow-hidden">
        <h5 className="text-2xl sm:text-[28px] font-extrabold text-white text-left">
          Use Restore Code
        </h5>

        <p className="text-base sm:text-[18px] font-semibold text-white text-left">
          Paste your restoral code to proceed.
        </p>

        <Formik
          initialValues={{ restore_code: '' }}
          validationSchema={restoreCodeSchema}
          onSubmit={handleSubmitForm}
        >
          {({ setFieldValue, errors, touched }) => (
            <Form>
              <Field name="restore_code">
                {({ field, meta }) => (
                  <div className="mb-4">
                    <input
                      type="text"
                      placeholder="XXXX-XXXX-XXXX-XXXX-XXXX-XXXX"
                      value={restoreCode}
                      onChange={(e) => handleInputChange(e, setFieldValue)}
                      onPaste={(e) => handlePaste(e, setFieldValue)}
                      className={`w-full px-4 py-3 bg-white/10 border ${
                        meta.touched && meta.error ? 'border-red-500' : 'border-white/20'
                      } rounded-md text-white placeholder-white/60 font-mono text-center tracking-wider`}
                      maxLength={29} // 24 characters + 5 dashes
                      style={{
                        fontSize: '16px',
                        letterSpacing: '2px'
                      }}
                    />
                    {meta.touched && meta.error && (
                      <InputError message={meta.error} />
                    )}
                  </div>
                )}
              </Field>

              <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 mt-6">
                <Button
                  type="submit"
                  disabled={isSubmitting || !restoreCode || restoreCode.length < 29}
                  className="bg-[#00b7ff] text-white font-semibold !rounded-md w-full sm:w-[200px] disabled:opacity-50"
                >
                  {isSubmitting ? 'Verifying...' : 'Continue'}
                </Button>
                <Button
                  type="button"
                  onClick={handleClose}
                  disabled={isSubmitting}
                  className="!bg-[#B4B4B4] text-black font-semibold !rounded-md w-full sm:w-[200px]"
                >
                  Cancel
                </Button>
              </div>
            </Form>
          )}
        </Formik>

        <div className="mt-4 text-sm text-white/80">
          <div className="flex items-start gap-2">
            <span className="text-[#00b7ff] mt-1">💡</span>
            <span>
              Don't have a code? Generate one from your account settings next time you log in.
            </span>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default UseRestoreCodeModal;
