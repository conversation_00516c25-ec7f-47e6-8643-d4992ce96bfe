{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_570db516._.js", "server/edge/chunks/[root of the server]__a8b25e96._.js", "server/edge/chunks/edge-wrapper_d6f08048.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/admin(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/admin/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/user(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/user/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/pricing(\\\\.json)?[\\/#\\?]?$", "originalSource": "/pricing"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/change-password(\\\\.json)?[\\/#\\?]?$", "originalSource": "/change-password"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/account(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/account/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/dashboard(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/dashboard/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/super-admin(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/super-admin/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/not-found(\\\\.json)?[\\/#\\?]?$", "originalSource": "/not-found"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/security-check(\\\\.json)?[\\/#\\?]?$", "originalSource": "/security-check"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JAdWpMZW7YX2HWGBSRb2tC6MF8pA5fg8t+vFkOc3uBA=", "__NEXT_PREVIEW_MODE_ID": "1d8d68f0710cb5a09c9f6c51eeb21850", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d435d7e5e6efd0ec0719bb7ec03080a3c3b988d4bac15f3d2e24eb0697f913c6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d3032946dcefd3747a266d31e7a13d7bdbe5eb44383f132b298cff85503a11b1"}}}, "sortedMiddleware": ["/"], "functions": {}}