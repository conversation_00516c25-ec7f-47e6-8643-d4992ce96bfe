/* [project]/src/css/Home/EducationDetail.scss.css [app-client] (css) */
:root {
  --font-gilroy: "<PERSON><PERSON>", sans-serif;
}

@media (width <= 991px) {
  .education_detail {
    padding-top: 40px !important;
  }
}

.education_detail_tag {
  padding: 6px 20px;
  background-color: #00adef;
  border-radius: 10px;
  text-align: center;
  font-size: 16px;
  font-weight: 600;
  line-height: 26px;
  letter-spacing: -.1px;
  text-transform: uppercase;
  color: #fff;
  border: 0;
}

.education_detail_heading h1 {
  font-size: 2.8rem;
  font-weight: 600;
  color: #fff;
  padding: 30px 0;
}

@media (width <= 1199px) {
  .education_detail_heading h1 {
    font-size: 2.5rem;
  }
}

@media (width <= 767px) {
  .education_detail_heading h1 {
    font-size: 1.5rem;
  }
}

@media (width <= 390px) {
  .education_detail_heading h1 {
    font-size: 1.3rem;
  }
}

.education_detail_heading h5 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #fff;
  padding-top: 30px;
}

.education_detail_postimg {
  padding: 5rem 0;
}

@media (width <= 767px) {
  .education_detail_postimg {
    padding: .625rem 0 2rem;
  }
}

.education_detail_postimg img {
  border-radius: 60px;
}

@media (width <= 767px) {
  .education_detail_postimg img {
    border-radius: 30px;
  }
}

.education_detail_text h2 {
  font-size: 4rem;
  overflow-wrap: break-word;
}

@media (width <= 1269px) {
  .education_detail_text h2 {
    font-size: 3rem !important;
  }
}

@media (width <= 991px) {
  .education_detail_text h2 {
    font-size: 3rem !important;
  }
}

@media (width <= 767px) {
  .education_detail_text h2 {
    font-size: 2.5rem !important;
  }
}

.education_detail_text p {
  font-size: 1.5rem;
  font-weight: 400;
  line-height: 36px;
  letter-spacing: -.1px;
  color: #fff;
  padding-top: 20px;
  overflow-wrap: break-word;
}

@media (width <= 767px) {
  .education_detail_text p {
    font-size: 15px;
    line-height: 23px;
    padding-top: 0;
  }
}

.education_detail_author {
  padding-top: 5rem;
}

@media (width <= 767px) {
  .education_detail_author {
    padding-top: 3rem;
  }
}

.education_detail_author_btn {
  background-color: #0000;
  border: 0;
  color: #00adef;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 24.5px;
  letter-spacing: -.1px;
  margin-bottom: 60px;
}

@media (width <= 767px) {
  .education_detail_author_btn {
    font-size: 1rem;
    line-height: 1.25rem;
    margin-bottom: 30px;
  }
}

.education_detail .recent_post {
  background-color: #0000;
  border-radius: 0;
  border: 0;
  margin-bottom: 0;
  padding: 30px 0;
  border-top: 1px solid #666;
  border-bottom: 1px solid #666;
}

.education_detail_sidebar {
  position: relative;
}

@media screen and (width >= 992px) {
  .education_detail_sidebar {
    position: sticky;
    top: 100px;
  }
}

.education_detail_sidebar .collapse_btn {
  position: fixed;
  background-color: #00adef;
  width: 40px;
  height: 40px;
  align-items: center;
  justify-content: center;
  right: 0;
  border-radius: 15px 0 0 15px;
  top: 90px;
  z-index: 99;
  display: none;
}

@media screen and (width <= 991px) {
  .education_detail_sidebar .collapse_btn {
    display: flex;
  }
}

@media screen and (width <= 767px) {
  .education_detail_sidebar .collapse_btn {
    top: 70px;
  }
}

@media screen and (width <= 1599px) {
  .education_detail_sidebar .btn-style {
    font-size: 1rem;
  }
}

.education_detail_sidebar_collapse {
  position: relative;
}

@media screen and (width <= 991px) {
  .education_detail_sidebar_collapse {
    margin-top: 0;
    position: fixed;
    width: 100%;
    right: -100%;
    transition: all .2s ease-in-out;
    z-index: 98;
    top: 72px;
  }
}

@media screen and (width <= 767px) {
  .education_detail_sidebar_collapse {
    top: 56px;
  }
}

@media screen and (width <= 991px) {
  .education_detail_sidebar_collapse .collapse_wrap {
    background-color: #031940;
    border-radius: 20px;
    padding: 70px 20px 20px;
    height: calc(100vh - 36px);
  }

  .education_detail_sidebar_collapse.active {
    right: 0;
  }
}

.education_detail_sidebar_collapse.active .collapse_wrap {
  display: block;
}

@media screen and (width <= 991px) {
  .education_detail_sidebar_top {
    display: flex;
  }

  .education_detail_sidebar_top .btn-style {
    margin-right: 10px;
    width: 50% !important;
  }

  .education_detail_sidebar_top .education_search {
    width: 50%;
    margin-left: 10px;
    margin-top: 0 !important;
  }
}

@media screen and (width <= 767px) {
  .education_detail_sidebar_top {
    flex-direction: column;
    align-items: center;
  }

  .education_detail_sidebar_top .btn-style {
    margin-right: 0;
    margin-bottom: 10px;
    width: 400px !important;
  }

  .education_detail_sidebar_top .education_search {
    width: 400px;
    margin-left: 0;
    margin-top: 10px !important;
  }
}

@media screen and (width <= 575px) {
  .education_detail_sidebar_top {
    display: block;
  }

  .education_detail_sidebar_top .btn-style {
    width: 100% !important;
  }

  .education_detail_sidebar_top .education_search {
    width: 100%;
  }
}

@media (width >= 992px) {
  .education_detail_sidebar_profit {
    max-height: calc(100vh - 480px);
    overflow-y: auto;
    overflow-x: clip;
    padding-right: 5px;
  }
}

@media screen and (width <= 991px) {
  .education_detail_sidebar_profit {
    display: flex;
    overflow: hidden;
    position: relative;
    max-width: 90%;
    width: 100%;
    margin: 0 auto;
  }
}

@media screen and (width <= 767px) {
  .education_detail_sidebar_profit {
    max-width: 240px;
  }
}

.education_detail_sidebar_profit_inner {
  border-bottom: 1px solid #fff3;
  padding: 10px 0;
}

@media screen and (width <= 991px) {
  .education_detail_sidebar_profit_inner {
    min-width: 300px;
    margin-right: 30px;
  }
}

@media screen and (width <= 767px) {
  .education_detail_sidebar_profit_inner {
    min-width: 100%;
    margin-right: 0;
    border-bottom: 0;
  }
}

.education_detail_sidebar_profit_inner_detail {
  cursor: pointer;
}

.education_detail_sidebar_profit_img {
  width: 130px;
}

@media screen and (width <= 767px) {
  .education_detail_sidebar_profit_img {
    width: 100px;
  }
}

.education_detail_sidebar_profit_img img {
  border-radius: 20px;
  height: 80px;
  object-fit: cover;
}

@media screen and (width <= 767px) {
  .education_detail_sidebar_profit_img img {
    height: 60px;
  }
}

.education_detail_sidebar_profit_text {
  width: calc(100% - 130px);
  padding-left: 5px;
}

@media screen and (width <= 767px) {
  .education_detail_sidebar_profit_text {
    width: calc(100% - 100px);
  }
}

.education_detail_sidebar_profit_text h6 {
  font-size: 1rem;
  font-weight: 600;
  color: #fff;
  padding-bottom: 10px;
}

@media screen and (width <= 767px) {
  .education_detail_sidebar_profit_text h6 {
    padding-bottom: 6px;
  }
}

.education_detail_sidebar_profit_text p {
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  text-align: left;
  color: #c5c5d5;
  word-wrap: break-word;
  white-space: normal;
}

@media screen and (width <= 767px) {
  .education_detail_sidebar_profit_text p {
    font-size: 14px;
    line-height: 15px;
  }
}

.education_detail_sidebar_profit_progressbar {
  margin-top: 10px;
}

.education_detail_sidebar_profit_progressbar .progress {
  height: 7px;
  background-color: #fff3;
}

.education_detail_sidebar_profit_progressbar .progress .progress-bar {
  background-color: #00adef;
}

.education_detail_sidebar_article {
  margin: 20px 0 0;
  text-align: center;
}

@media screen and (width <= 991px) {
  .education_detail_sidebar_article {
    margin: 40px 0 0;
  }
}

.education_detail_sidebar_article_data h6 {
  padding-bottom: 10px;
}

.education_detail .scroll-btn {
  background-color: #00adef;
  color: #fff;
  border: none;
  padding: 0;
  cursor: pointer;
  font-size: 1.2rem;
  min-width: 30px;
  min-height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10rem;
  position: absolute;
  display: none;
  top: 50%;
  transform: translateY(-50%);
}

@media (width <= 991px) {
  .education_detail .scroll-btn {
    display: flex;
  }
}

.education_detail .scroll-btn.left {
  left: -10px;
}

.education_detail .scroll-btn.left svg {
  transform: rotate(180deg);
}

.education_detail .scroll-btn.right {
  right: -10px;
}

.education_detail .scroll-btn:hover {
  background-color: #00adef;
}

.education_detail .scroll-btn.disabled, .education_detail .scroll-btn:disabled {
  background-color: #414c60;
}

@media screen and (width <= 991px) {
  .education_detail .commonSearch {
    max-width: 100%;
  }
}

.CircularProgressbar .CircularProgressbar-trail {
  stroke: #fff3;
}

.CircularProgressbar .CircularProgressbar-path {
  stroke: #00adef;
}

.CircularProgressbar_text {
  text-align: center;
}

.CircularProgressbar_text h6 {
  font-size: .875rem;
  font-weight: 600;
  fill: #fff;
  padding-bottom: 5px;
}

/*# sourceMappingURL=src_css_Home_EducationDetail_scss_b52d8e88.css.map*/