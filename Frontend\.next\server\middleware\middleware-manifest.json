{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_570db516._.js", "server/edge/chunks/[root of the server]__a8b25e96._.js", "server/edge/chunks/edge-wrapper_d6f08048.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/admin/:path*{(\\\\.json)}?", "originalSource": "/admin/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/user/:path*{(\\\\.json)}?", "originalSource": "/user/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/pricing{(\\\\.json)}?", "originalSource": "/pricing"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/change-password{(\\\\.json)}?", "originalSource": "/change-password"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/account/:path*{(\\\\.json)}?", "originalSource": "/account/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/super-admin/:path*{(\\\\.json)}?", "originalSource": "/super-admin/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/not-found{(\\\\.json)}?", "originalSource": "/not-found"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/security-check{(\\\\.json)}?", "originalSource": "/security-check"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JAdWpMZW7YX2HWGBSRb2tC6MF8pA5fg8t+vFkOc3uBA=", "__NEXT_PREVIEW_MODE_ID": "1b5f7cefe9d103368e438a9402ae1d6d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1cadc3734f6c8cbaed3e4bb7510e5ecf36f7ce8af8791d6163144e414e8af2d3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9fdeac83e66056e1d1fa3e8f0c456e640543fd6b7e2ffbe6c231f4903ed2e58d"}}}, "instrumentation": null, "functions": {}}