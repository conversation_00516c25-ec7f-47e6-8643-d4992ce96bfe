(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/[turbopack]_browser_dev_hmr-client_hmr-client_ts_27cbde85._.js", {

"[turbopack]/browser/dev/hmr-client/hmr-client.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/[turbopack]_browser_dev_hmr-client_hmr-client_ts_61dcf9ba._.js",
  "static/chunks/[turbopack]_browser_dev_hmr-client_hmr-client_ts_2b17bd2e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[turbopack]/browser/dev/hmr-client/hmr-client.ts [app-client] (ecmascript)");
    });
});
}}),
}]);