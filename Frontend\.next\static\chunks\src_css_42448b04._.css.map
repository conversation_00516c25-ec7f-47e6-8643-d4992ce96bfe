{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/css/common/CommonButton.scss.css"], "sourcesContent": [":root{--font-gilroy: \"<PERSON><PERSON>\", sans-serif}.btn-style,.btn-primary{min-height:66px;display:inline-flex;justify-content:center;align-items:center;text-align:center;border-radius:10rem;padding:.5rem 1.5rem;font-size:1.25rem;font-weight:600;background-color:#00adef;border:0;text-transform:capitalize;transition:all ease-in-out .3s;min-width:150px;color:#fff}.btn-style span,.btn-primary span{line-height:1}@media(max-width: 1599px){.btn-style,.btn-primary{min-height:66px}}@media(max-width: 1199px){.btn-style,.btn-primary{min-height:56px;font-size:1.125rem;font-weight:500}}@media(max-width: 767px){.btn-style,.btn-primary{min-height:46px;font-size:1rem}}.btn-style:hover,.btn-primary:hover{background-color:#0099d1;color:#fff}.btn-style.transparent,.btn-primary.transparent{background-color:rgba(0,0,0,0);border:none}.btn-style.white-btn,.btn-primary.white-btn{background:#fff;color:#000}.btn-style.white-btn:hover,.btn-primary.white-btn:hover{background:#00adef;color:#fff}.btn-style.yellow-btn,.btn-primary.yellow-btn{background-color:#fea500;color:#fff}.btn-style.yellow-btn:hover,.btn-primary.yellow-btn:hover{background-color:#c9870d;color:#fff}.btn-style.gray-btn,.btn-primary.gray-btn{background-color:#5e6165 !important;color:#fff}.btn-style.gray-btn:hover,.btn-primary.gray-btn:hover{background-color:#708090;color:#fff}.btn-style.gradient-btn,.btn-primary.gradient-btn{background:linear-gradient(75deg, #00aeef, #1f5aff 50.31%, #da00ff);color:#fff}.btn-style.gradient-btn:hover,.btn-primary.gradient-btn:hover{background:linear-gradient(75deg, #0043ff, #1f5aff 50.31%, #da00ff);color:#fff}.btn-style.green-btn,.btn-primary.green-btn{background-color:#32cd33;color:#fff}.btn-style.green-btn:hover,.btn-primary.green-btn:hover{background-color:#2bb72b;color:#fff}.btn-style.red-btn,.btn-primary.red-btn{background-color:#ff696a;color:#fff}.btn-style.red-btn:hover,.btn-primary.red-btn:hover{background-color:#e65f60;color:#fff}.btn-style.border-btn,.btn-primary.border-btn{background:rgba(0,0,0,0);color:#fff;border:1px solid #00adef}.btn-style.border-btn:hover,.btn-primary.border-btn:hover{background:#00adef;color:#fff}.btn-style .onlyIcon,.btn-primary .onlyIcon{margin-right:15px;display:inline-flex}.btn-style:disabled,.btn-style.disabled,.btn-primary:disabled,.btn-primary.disabled{background:#c5c5d5;color:#fff;cursor:not-allowed;opacity:1}:disabled,.disabled{background-color:#414c60;color:#fff;cursor:not-allowed;opacity:1}.white20{background-color:hsla(0,0%,100%,.1215686275);width:100%}"], "names": [], "mappings": "AAAA;;;;AAA0C;;;;;;;;;;;;;;;;;;AAAyT;;;;AAAgD;EAA0B;;;;;AAAyC;EAA0B;;;;;;;AAA4E;EAAyB;;;;;;AAAwD;;;;;AAAwE;;;;;AAA2F;;;;;AAAuE;;;;;AAAsF;;;;;AAAkF;;;;;AAA8F;;;;;AAAyF;;;;;AAA0F;;;;;AAAiI;;;;;AAA6I;;;;;AAAgF;;;;;AAA4F;;;;;AAA4E;;;;;AAAwF;;;;;;AAA2G;;;;;AAAwF;;;;;AAAkF;;;;;;;AAA+I;;;;;;;AAAqF", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/css/auth/authGlobals.scss.css"], "sourcesContent": [":root{--font-g<PERSON>roy: \"<PERSON><PERSON>\", sans-serif}.loginCommon .referralCol{overflow-y:auto;overflow-x:clip;height:100vh;width:603px}@media(min-width: 1400px){.loginCommon .referralCol{width:45%}}.loginCommon .referralCol img{width:100%;height:100%;object-fit:cover}.loginCommon .loginCol{display:flex;align-items:center;justify-content:center;width:100%}@media(min-width: 992px){.loginCommon .loginCol{width:calc(100% - 603px)}}@media(min-width: 1400px){.loginCommon .loginCol{width:55%}}@media(max-width: 991px){.loginCommon .loginCol{min-height:100vh}}.loginCommon_rightSide{padding:1rem 1rem 1rem;width:100%}@media(max-width: 991px){.loginCommon_rightSide{padding:3rem 1rem 1.5rem}}.loginCommon_rightSide_inner{max-width:553px;margin:0 auto}.loginCommon_rightSide_formBox{width:100%;background-color:rgba(159,159,159,.1);border-radius:50px;padding:2.5rem 4.35rem 2.5rem;position:relative;z-index:1;backdrop-filter:blur(4px)}@media(max-width: 991px){.loginCommon_rightSide_formBox{padding:2rem 1rem 2rem}}.loginCommon_rightSide_formBox::after{content:\"\";position:absolute;top:0px;left:-90px;width:100%;height:100%;background-image:url(\"https://cdn.tradereply.com/dev/site-assets/tradereply-for-stock-traders.png\");background-size:100% 100%;background-repeat:no-repeat;z-index:-1}.loginCommon_rightSide .thirdParty_login{margin-top:1.25rem}.loginCommon_rightSide .thirdParty_login_btn{background-color:hsla(0,0%,100%,.9);width:30px;height:30px;border-radius:.625rem;display:flex;align-items:center;justify-content:center;cursor:pointer;margin-right:1rem;border:0;transition:all ease-in-out .3s}.loginCommon_rightSide .thirdParty_login_btn:last-child{margin-right:0}.loginCommon_rightSide .thirdParty_login_btn:hover{background-color:#fff}.loginCommon_rightSide .orLine{margin:1.25rem 0;text-align:center;position:relative}.loginCommon_rightSide .orLine span{color:#fff;display:inline-block;margin:0 auto}.loginCommon_rightSide .orLine::after,.loginCommon_rightSide .orLine::before{content:\"\";position:absolute;top:50%;left:0;width:30%;height:1px;background-color:#666}@media(max-width: 575px){.loginCommon_rightSide .orLine::after,.loginCommon_rightSide .orLine::before{width:25%}}.loginCommon_rightSide .orLine::before{left:auto;right:0}.loginCommon .forgot_form .orLine::after,.loginCommon .forgot_form .orLine::before{content:\"\";position:absolute;top:50%;left:0;width:20%;height:1px;background-color:#666}@media(max-width: 575px){.loginCommon .forgot_form .orLine::after,.loginCommon .forgot_form .orLine::before{width:15%}}.loginCommon .forgot_form .orLine::before{left:auto;right:0}.loginCommon .loginHeading h1{font-size:1.25rem;font-weight:600;color:#fff;margin-bottom:6px;text-align:center}@media(max-width: 991px){.loginCommon .loginHeading h1{font-size:1rem}}.loginCommon .Forgotpassoword{margin-top:-1rem}.loginCommon .Forgotpassoword a{font-weight:700;color:#35c7ff}@media(max-width: 1199px){.loginCommon .Forgotpassoword a{font-size:1rem}}.loginCommon .Forgotpassoword a:hover{color:#00adef;opacity:.8}.loginCommon .anAccount h6{font-size:16px;font-weight:600;line-height:26px;text-align:center;color:#00adef;max-width:266px;margin:0 auto}.loginCommon .signup_form .anAccount h6{color:#c5c5c5}.loginCommon .login_footer_links{display:flex;justify-content:center;margin-bottom:10px}.loginCommon .login_footer_links a{color:#c5c5d5;font-size:15px;line-height:12px;font-weight:400;text-transform:uppercase;border-right:1px solid #c5c5d5;padding:0 .625rem}@media(max-width: 991px){.loginCommon .login_footer_links a{font-size:14px;padding:0 .625rem;margin-top:.5rem}}.loginCommon .login_footer_links a:last-child{border-right:0}.loginCommon .login_footer_links a:hover{color:#00adef}.loginCommon .login_footer_links p{font-size:18px;font-weight:400}.loginCommon .backbtn{margin-bottom:20px}.loginCommon .backbtn a{display:flex;align-items:center;font-size:1.125rem;font-weight:600;color:#fff}.loginCommon .backbtn a img,.loginCommon .backbtn a svg{transform:rotate(180deg);margin-right:10px;transition:all ease-in-out .3s}.loginCommon .backbtn a:hover{color:#00adef}.loginCommon .backbtn a:hover img,.loginCommon .backbtn a:hover svg{margin-right:12px}.loginCommon .backbtn a:hover img path,.loginCommon .backbtn a:hover svg path{fill:#00adef}.authEmailBg{background-color:#011132;display:flex;justify-content:center;margin:20px 0}.authEmailBg_authContainer{width:650px;padding:20px}.authEmailBg_authContainer img{height:80px;margin-bottom:15px}.authEmailBg_authContainer_inner{background-color:#1f2a3e;width:100%;padding:25px 15px;border-radius:10px;border:1px solid hsla(0,0%,100%,.3137254902)}.authEmailBg_authContainer_inner p{font-size:14px;margin-bottom:15px}.authEmailBg_authContainer_inner p a{margin-bottom:0 !important}.authEmailBg_authContainer_inner a{margin-bottom:15px}.authEmailBg_authContainer_inner a button{background-color:#00adef;padding:10px 25px;color:#fff;border-radius:6px;font-size:24px;font-weight:600}.authEmailBg_authContainer_inner_user p:first-child{margin-bottom:0px !important}.authEmailBg_authContainer_inner_user span{color:#2ec735}.authEmailBg_authContainer_outer{margin-top:20px}.authEmailBg_authContainer_outer p{font-size:14px;text-align:center;margin-bottom:5px}.authCorrectIcon{position:relative}.authCorrectIcon .checkIcon{position:absolute;left:-40px;top:18px}.authCorrectIcon svg{height:22}.invalid_credential{display:flex;gap:10px;align-items:center;border:1px solid #ff696a !important;background-color:#1e222d;width:fit-content;padding:10px;border-radius:10px;font-size:14px;font-weight:600}.signup_invalid_credential{border:1px solid #ff696a !important;background-color:#1e222d;width:fit-content;padding:10px;border-radius:10px}.signup_invalid_credential p,.signup_invalid_credential span{font-size:14px}.signup_invalid_credential p a,.signup_invalid_credential span a{font-size:16px !important;font-weight:700}.session-expire-message{background-color:#ffa9a9;color:#000;border-radius:2rem;padding:10px 30px;text-align:center;font-weight:600;width:fit-content;margin-bottom:10px}"], "names": [], "mappings": "AAAA;;;;AAA0C;;;;;;;AAAmF;EAA0B;;;;;AAAqC;;;;;;AAAsE;;;;;;;AAAyF;EAAyB;;;;;AAAiD;EAA0B;;;;;AAAkC;EAAyB;;;;;AAAyC;;;;;AAAyD;EAAyB;;;;;AAAiD;;;;;AAA2D;;;;;;;;;;AAAuL;EAAyB;;;;;AAAuD;;;;;;;;;;;;;AAAkR;;;;AAA4D;;;;;;;;;;;;;;AAA8P;;;;AAAuE;;;;AAAyE;;;;;;AAAoF;;;;;;AAAkF;;;;;;;;;;AAAoK;EAAyB;;;;;AAAwF;;;;;AAAyD;;;;;;;;;;AAA0K;EAAyB;;;;;AAA8F;;;;;AAA4D;;;;;;;;AAA+G;EAAyB;;;;;AAA8C;;;;AAA+C;;;;;AAA8D;EAA0B;;;;;AAAgD;;;;;AAA+D;;;;;;;;;;AAAyI;;;;AAAsD;;;;;;AAAwF;;;;;;;;;;AAA2K;EAAyB;;;;;;;AAAsF;;;;AAA6D;;;;AAAuD;;;;;AAAkE;;;;AAAyC;;;;;;;;AAAsG;;;;;;AAAkI;;;;AAA4C;;;;AAAsF;;;;AAA2F;;;;;;;AAAwF;;;;;AAAoD;;;;;AAA8D;;;;;;;;AAAuJ;;;;;AAAqE;;;;AAAgE;;;;AAAsD;;;;;;;;;AAAiJ;;;;AAAiF;;;;AAAyD;;;;AAAiD;;;;;;AAAsF;;;;AAAmC;;;;;;AAAkE;;;;AAA+B;;;;;;;;;;;;;AAA2M;;;;;;;;AAA0I;;;;AAA4E;;;;;AAA2G", "debugId": null}}, {"offset": {"line": 562, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 565, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/css/common/textInput.scss.css"], "sourcesContent": [":root{--font-g<PERSON>roy: \"<PERSON><PERSON>\", sans-serif}.form-label,.form-check-label{display:block;color:#fff !important;font-size:1rem;font-weight:400;line-height:normal;margin-bottom:.625rem}@media(max-width: 1199px){.form-label,.form-check-label{font-size:.875rem}}.spanInputCounter{position:absolute;right:7px;top:39%;font-size:14px}.customInput{margin-bottom:1.25rem;line-height:normal}@media(max-width: 767px){.customInput{margin-bottom:1rem}}.customInput_inner{position:relative}.customInput_inner .allocP{position:absolute;top:13px;right:1rem;color:#9c9a9f;font-size:1rem;font-weight:500}.customInput label{display:block;color:#9c9a9f;font-size:1rem;font-weight:400;line-height:normal;margin-bottom:.625rem}@media(max-width: 1199px){.customInput label{font-size:.875rem}}.customInput .form-control{min-height:56px;box-shadow:none;outline:none;width:100%;padding:.5rem 1.25rem;border-radius:1rem;border:1px solid hsla(0,0%,100%,.3);background-color:hsla(0,0%,100%,.3);color:#fff;font-size:1rem}@media(max-width: 1599px){.customInput .form-control{min-height:52px;font-size:1rem}}.customInput .form-control:hover{appearance:none}.customInput .form-control::placeholder{color:#fff;opacity:.7}.customInput .form-control:disabled{background-color:rgba(0,0,0,0)}.customInput .form-control:focus{box-shadow:none;border:1px solid hsla(0,0%,100%,.3);background-color:hsla(0,0%,100%,.3);color:#fff}.customInput .form-control.passwordInput{padding-right:4.375rem}.customInput.passwordInput .form-control{padding-right:3.75rem}.customInput .eyeIcon{position:absolute;right:1rem;top:50%;transform:translateY(-50%);cursor:pointer;display:flex}.customInput .eyeIcon svg{width:1.25rem;height:1.25rem}.customInput .eyeIcon svg path{fill:#fff}.checkbox_input .form-check{margin-bottom:0;padding:0;display:flex;align-items:center}.checkbox_input .form-check .form-check-input{float:unset;margin:0;box-shadow:none;width:20px !important;height:20px !important;cursor:pointer;background-color:rgba(0,0,0,0);border:1px solid #666}.checkbox_input .form-check .form-check-input:checked{border-color:#00adef}.checkbox_input .form-check .form-check-input:checked[type=radio]{background-size:10px}.checkbox_input .form-check .form-check-label{line-height:1;margin-left:10px}.checkbox_input .form-check.form-switch .form-check-input{width:72px !important;height:34px !important;background-color:#fff;border-color:#666;background-size:30px}.checkbox_input .form-check.form-switch .form-check-input:checked{background-color:#00adef;border-color:#00adef}.error-field{border:1px solid #ff696a !important}.error-message{color:#ff696a;font-size:16px;line-height:normal;display:block;padding-top:5px;font-weight:700}.form-control{min-height:56px;box-shadow:none;outline:none;width:100%;padding:.5rem 1.25rem;border-radius:1rem;border:1px solid hsla(0,0%,100%,.3);background-color:hsla(0,0%,100%,.3);color:#000;font-size:1rem}@media(max-width: 1599px){.form-control{min-height:52px;font-size:1rem}}.form-control:hover{appearance:none}.form-control::placeholder{color:#fff}.form-control:disabled{background-color:rgba(0,0,0,0)}.form-control:focus{box-shadow:none;border:1px solid hsla(0,0%,100%,.3);background-color:hsla(0,0%,100%,.3);color:#fff}.form-control.passwordInput{padding-right:4.375rem}.inputLabel{font-size:15px;font-weight:600;color:hsla(0,0%,100%,.8);margin-bottom:10px}"], "names": [], "mappings": "AAAA;;;;AAA0C;;;;;;;;;AAA0I;EAA0B;;;;;AAAiD;;;;;;;AAAqE;;;;;AAAsD;EAAyB;;;;;AAAiC;;;;AAAqC;;;;;;;;;AAA8G;;;;;;;;;AAAuH;EAA0B;;;;;AAAsC;;;;;;;;;;;;;AAA8N;EAA0B;;;;;;AAA2D;;;;AAAiD;;;;;AAA8D;;;;AAAmE;;;;;;;AAAoI;;;;AAAgE;;;;AAA+D;;;;;;;;;AAAkH;;;;;AAAuD;;;;AAAyC;;;;;;;AAAsF;;;;;;;;;;;AAAoM;;;;AAA2E;;;;AAAuF;;;;;AAA6E;;;;;;;;AAAoK;;;;;AAAgH;;;;AAAiD;;;;;;;;;AAA6G;;;;;;;;;;;;;AAAiN;EAA0B;;;;;;AAA8C;;;;AAAoC;;;;AAAsC;;;;AAAsD;;;;;;;AAAuH;;;;AAAmD", "debugId": null}}, {"offset": {"line": 802, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 805, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/css/account/Security.scss.css"], "sourcesContent": [":root{--font-gilroy: \"<PERSON><PERSON>\", sans-serif}.security_sec .account_card_list ul li{justify-content:flex-start}.security_sec .account_card_list ul li span{padding-right:15px}.security_sec .account_card_list p{color:#c5c5d5}.security_sec .table_heading{border:1px solid #666;border-radius:15px;padding:10px 1.25rem}.modal-content{background-color:#031940 !important;border:1px solid #04498c;border-radius:15px;color:#fff}.account_card_list_btns{display:flex;justify-content:end;align-items:center;gap:10px}@media(max-width: 360px){.account_card_list_btns{display:flex;justify-content:center;align-items:center;gap:5px}.confirm-modal-btn{justify-content:center !important;gap:8px !important;flex-wrap:wrap}}"], "names": [], "mappings": "AAAA;;;;AAA0C;;;;AAAkE;;;;AAA+D;;;;AAAiD;;;;;;AAA2F;;;;;;;AAA0G;;;;;;;AAAqF;EAAyB;;;;;;;EAAuF", "debugId": null}}, {"offset": {"line": 855, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}