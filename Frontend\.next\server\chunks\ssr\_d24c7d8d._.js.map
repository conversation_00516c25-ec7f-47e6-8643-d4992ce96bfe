{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/education/%5Bdetail%5D/components/EducationContent.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(Home)/education/[detail]/components/EducationContent.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(Home)/education/[detail]/components/EducationContent.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoU,GACjW,kGACA", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/education/%5Bdetail%5D/components/EducationContent.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(Home)/education/[detail]/components/EducationContent.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(Home)/education/[detail]/components/EducationContent.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgT,GAC7U,8EACA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/Header.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Components/UI/Header.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Components/UI/Header.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/Header.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Components/UI/Header.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Components/UI/Header.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2Q,GACxS,yCACA", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/Footer.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Components/UI/Footer.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Components/UI/Footer.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/Footer.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Components/UI/Footer.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Components/UI/Footer.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2Q,GACxS,yCACA", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Layouts/HomeLayout.js"], "sourcesContent": ["// 'use client';\r\n\r\nimport \"bootstrap/dist/css/bootstrap.min.css\";\r\nimport { Fragment } from \"react\";\r\nimport Header from \"@/Components/UI/Header\";\r\nimport Footer from \"@/Components/UI/Footer\";\r\nimport { LanguageProvider } from \"@/context/LanguageContext\";\r\n\r\nconst HomeLayout = ({ children }) => {\r\n  return (\r\n    <Fragment>\r\n      <LanguageProvider>\r\n        <Header />\r\n        {/* Wrap main content in <main> for SEO & accessibility */}\r\n        <main className=\"main-content\">{children}</main>\r\n        <Footer />\r\n      </LanguageProvider>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default HomeLayout;\r\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;;AAGhB;AACA;AACA;AACA;;;;;;;AAEA,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAE;IAC9B,qBACE,8OAAC,qMAAA,CAAA,WAAQ;kBACP,cAAA,8OAAC,iIAAA,CAAA,mBAAgB;;8BACf,8OAAC,iIAAA,CAAA,UAAM;;;;;8BAEP,8OAAC;oBAAK,WAAU;8BAAgB;;;;;;8BAChC,8OAAC,iIAAA,CAAA,UAAM;;;;;;;;;;;;;;;;AAIf;uCAEe", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 194, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/education/%5Bdetail%5D/page.js"], "sourcesContent": ["import { unstable_noStore as noStore } from 'next/cache';\r\nimport EducationContent from \"./components/EducationContent\";\r\nimport HomeLayout from \"@/Layouts/HomeLayout\";\r\nimport MetaHead from \"@/Seo/Meta/MetaHead\";\r\nimport { Container } from \"react-bootstrap\";\r\nimport { generateSchemaMetadata } from \"@/Seo/Schema/JsonLdSchema\";\r\n\r\nasync function fetchEducationDetail(detail) {\r\n  const url = new URL(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/article/education/${detail}`);\r\n\r\n  const res = await fetch(url.toString(), {\r\n    cache: \"no-store\",\r\n  });\r\n\r\n  if (!res.ok) throw new Error(`API error: ${res.status}`);\r\n  return res.json();\r\n}\r\n\r\nexport async function generateMetadata({ params }) {\r\n  noStore();\r\n\r\n  const resolvedParams = await params;\r\n  const detailSlug = resolvedParams.detail;\r\n  const data = await fetchEducationDetail(detailSlug);\r\n  const environment = process.env.NEXT_PUBLIC_ENVIRONMENT;\r\n  return {\r\n    title: `What is ${data.data.title} | TradeReply Education`,\r\n    description: data.data.summary,\r\n    openGraph: {\r\n      title: `What is ${data.data.title} | TradeReply Education`,\r\n      description: data.data.summary,\r\n      images: [{\r\n                url: data?.data?.feature_image_url || 'https://www.tradereply.com/images/tradereply-trading-analytics-og.jpg', // Replace with actual default image if needed\r\n                width: 1200,\r\n                height: 630,\r\n             }],\r\n    },\r\n     twitter: {\r\n         title: `What is ${data?.data?.title} | TradeReply Education`,\r\n         description: data?.data?.summary,\r\n         site: '@JoinTradeReply',\r\n         images: [data?.data?.feature_image_url || 'https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png'], // Replace with actual default image if needed\r\n       },\r\n       icons: {\r\n                icon: [\r\n                  {\r\n                    url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.ico`,\r\n                    type: \"image/x-icon\",\r\n                  },\r\n                  {\r\n                    url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.svg`,\r\n                    type: \"image/svg+xml\",\r\n                  },\r\n                ],\r\n              },\r\n  };\r\n}\r\n\r\nexport default async function EducationDetail({ params }) {\r\n  noStore();\r\n\r\n  const resolvedParams = await params;\r\n  const detailSlug = resolvedParams.detail;\r\n\r\n  const data = await fetchEducationDetail(detailSlug);\r\n\r\n  // Schema generation is now handled in generateMetadata function\r\n  return (\r\n    <HomeLayout>\r\n      <Container>\r\n        <EducationContent\r\n          detailSlug={detailSlug}\r\n          articleData={data.data}\r\n          nextArticle={data.next_article}\r\n          avgProgress={data.avgProgress}\r\n        />\r\n      </Container>\r\n    </HomeLayout>\r\n  );\r\n}\r\n\r\n// Generate metadata with JSON-LD schema for education pages\r\nexport async function generateMetadata({ params }) {\r\n  noStore();\r\n  const resolvedParams = await params;\r\n  const detail = resolvedParams.detail;\r\n  const data = await fetchEducationDetail(detail);\r\n  const articleData = data.data;\r\n\r\n  // Generate Article schema\r\n  const articleSchema = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"Article\",\r\n    \"mainEntityOfPage\": {\r\n      \"@type\": \"WebPage\",\r\n      \"@id\": `https://www.tradereply.com/education/${detail}`\r\n    },\r\n    \"headline\": articleData.title,\r\n    \"description\": articleData.summary,\r\n    \"author\": {\r\n      \"@type\": \"Organization\",\r\n      \"name\": \"TradeReply\"\r\n    },\r\n    \"publisher\": {\r\n      \"@type\": \"Organization\",\r\n      \"name\": \"TradeReply\",\r\n      \"logo\": {\r\n        \"@type\": \"ImageObject\",\r\n        \"url\": \"https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png\"\r\n      }\r\n    },\r\n    \"datePublished\": new Date(articleData.created_at).toISOString(),\r\n    \"dateModified\": new Date(articleData.updated_at || articleData.created_at).toISOString(),\r\n    \"articleSection\": \"Education\",\r\n    \"articleBody\": articleData.articleBody || \"\"\r\n  };\r\n\r\n  // Generate schema metadata\r\n  const schemaMetadata = generateSchemaMetadata([articleSchema]);\r\n\r\n  return {\r\n    title: `${articleData.title} | TradeReply Education`,\r\n    description: articleData.summary || 'Learn trading fundamentals with TradeReply education.',\r\n    robots: \"index, follow\",\r\n    openGraph: {\r\n      title: `${articleData.title} | TradeReply Education`,\r\n      description: articleData.summary,\r\n      siteName: 'TradeReply',\r\n      type: 'article',\r\n    },\r\n    twitter: {\r\n      title: `${articleData.title} | TradeReply Education`,\r\n      description: articleData.summary,\r\n    },\r\n    // Merge schema metadata into the main metadata object\r\n    ...schemaMetadata\r\n  };\r\n}"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,eAAe,qBAAqB,MAAM;IACxC,MAAM,MAAM,IAAI,IAAI,6DAAwC,0BAA0B,EAAE,QAAQ;IAEhG,MAAM,MAAM,MAAM,MAAM,IAAI,QAAQ,IAAI;QACtC,OAAO;IACT;IAEA,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,IAAI,MAAM,EAAE;IACvD,OAAO,IAAI,IAAI;AACjB;AAEO,eAAe,iBAAiB,EAAE,MAAM,EAAE;IAC/C,CAAA,GAAA,6HAAA,CAAA,mBAAO,AAAD;IAEN,MAAM,iBAAiB,MAAM;IAC7B,MAAM,aAAa,eAAe,MAAM;IACxC,MAAM,OAAO,MAAM,qBAAqB;IACxC,MAAM;IACN,OAAO;QACL,OAAO,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC;QAC1D,aAAa,KAAK,IAAI,CAAC,OAAO;QAC9B,WAAW;YACT,OAAO,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC;YAC1D,aAAa,KAAK,IAAI,CAAC,OAAO;YAC9B,QAAQ;gBAAC;oBACC,KAAK,MAAM,MAAM,qBAAqB;oBACtC,OAAO;oBACP,QAAQ;gBACX;aAAE;QACX;QACC,SAAS;YACL,OAAO,CAAC,QAAQ,EAAE,MAAM,MAAM,MAAM,uBAAuB,CAAC;YAC5D,aAAa,MAAM,MAAM;YACzB,MAAM;YACN,QAAQ;gBAAC,MAAM,MAAM,qBAAqB;aAA0E;QACtH;QACA,OAAO;YACE,MAAM;gBACJ;oBACE,KAAK,CAAC,2BAA2B,EAAE,YAAY,mCAAmC,CAAC;oBACnF,MAAM;gBACR;gBACA;oBACE,KAAK,CAAC,2BAA2B,EAAE,YAAY,mCAAmC,CAAC;oBACnF,MAAM;gBACR;aACD;QACH;IACZ;AACF;AAEe,eAAe,gBAAgB,EAAE,MAAM,EAAE;IACtD,CAAA,GAAA,6HAAA,CAAA,mBAAO,AAAD;IAEN,MAAM,iBAAiB,MAAM;IAC7B,MAAM,aAAa,eAAe,MAAM;IAExC,MAAM,OAAO,MAAM,qBAAqB;IAExC,gEAAgE;IAChE,qBACE,8OAAC,4HAAA,CAAA,UAAU;kBACT,cAAA,8OAAC,8LAAA,CAAA,YAAS;sBACR,cAAA,8OAAC,mLAAA,CAAA,UAAgB;gBACf,YAAY;gBACZ,aAAa,KAAK,IAAI;gBACtB,aAAa,KAAK,YAAY;gBAC9B,aAAa,KAAK,WAAW;;;;;;;;;;;;;;;;AAKvC;AAGO,eAAe,iBAAiB,EAAE,MAAM,EAAE;IAC/C,CAAA,GAAA,6HAAA,CAAA,mBAAO,AAAD;IACN,MAAM,iBAAiB,MAAM;IAC7B,MAAM,SAAS,eAAe,MAAM;IACpC,MAAM,OAAO,MAAM,qBAAqB;IACxC,MAAM,cAAc,KAAK,IAAI;IAE7B,0BAA0B;IAC1B,MAAM,gBAAgB;QACpB,YAAY;QACZ,SAAS;QACT,oBAAoB;YAClB,SAAS;YACT,OAAO,CAAC,qCAAqC,EAAE,QAAQ;QACzD;QACA,YAAY,YAAY,KAAK;QAC7B,eAAe,YAAY,OAAO;QAClC,UAAU;YACR,SAAS;YACT,QAAQ;QACV;QACA,aAAa;YACX,SAAS;YACT,QAAQ;YACR,QAAQ;gBACN,SAAS;gBACT,OAAO;YACT;QACF;QACA,iBAAiB,IAAI,KAAK,YAAY,UAAU,EAAE,WAAW;QAC7D,gBAAgB,IAAI,KAAK,YAAY,UAAU,IAAI,YAAY,UAAU,EAAE,WAAW;QACtF,kBAAkB;QAClB,eAAe,YAAY,WAAW,IAAI;IAC5C;IAEA,2BAA2B;IAC3B,MAAM,iBAAiB,CAAA,GAAA,oIAAA,CAAA,yBAAsB,AAAD,EAAE;QAAC;KAAc;IAE7D,OAAO;QACL,OAAO,GAAG,YAAY,KAAK,CAAC,uBAAuB,CAAC;QACpD,aAAa,YAAY,OAAO,IAAI;QACpC,QAAQ;QACR,WAAW;YACT,OAAO,GAAG,YAAY,KAAK,CAAC,uBAAuB,CAAC;YACpD,aAAa,YAAY,OAAO;YAChC,UAAU;YACV,MAAM;QACR;QACA,SAAS;YACP,OAAO,GAAG,YAAY,KAAK,CAAC,uBAAuB,CAAC;YACpD,aAAa,YAAY,OAAO;QAClC;QACA,sDAAsD;QACtD,GAAG,cAAc;IACnB;AACF", "debugId": null}}, {"offset": {"line": 346, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}